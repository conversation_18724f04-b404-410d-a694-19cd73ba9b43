# Remotion Audio Playback Fix

## Problem Description
User-uploaded voiceover audio from Supabase storage URLs was experiencing silent gaps at the beginning of playback in the Remotion preview player. This occurred because network-based audio URLs require loading time, while base64 audio worked perfectly.

## Root Cause Analysis
1. **Network Latency**: Supabase storage URLs require network requests to load audio data
2. **No Audio Preloading**: Remotion preview player didn't preload network audio before playback
3. **Base64 vs URL Difference**: Base64 audio is already in memory, while URLs need to be fetched
4. **Buffering Gap**: Audio components started "playing" before the actual audio data was available

## Solution Implementation

### 1. Created Audio Preloader Hook (`useAudioPreloader.ts`)
- **Location**: `src/lib/remotion/hooks/useAudioPreloader.ts`
- **Purpose**: Preloads network audio URLs before preview playback starts
- **Key Features**:
  - Only runs in PREVIEW mode (opposite of image preloader)
  - Uses `@remotion/preload.preloadAudio()` for efficient audio preloading
  - Uses Remotion's `useBufferState().delayPlayback()` to pause player
  - Skips base64 and blob URLs (already in memory)
  - Includes 5-second timeout to prevent infinite waiting
  - Deduplicates URLs to avoid redundant preloading
  - Graceful error handling - continues playback even if preloading fails

### 2. Added pauseWhenBuffering Props
- **Location**: `src/lib/remotion/components/core/ScenesVideo.tsx`
- **Purpose**: Fallback protection for any audio that wasn't preloaded
- **Implementation**: Added `pauseWhenBuffering` prop to all `<Audio>` components:
  - Speech audio (podcast/audio workflows)
  - Background music audio
  - Scene-based voiceover audio

### 3. Integration Point
- **Location**: `src/lib/remotion/compositions/UnifiedComposition.tsx`
- **Integration**: Added after image preloader with opposite condition
- **Code**: `useAudioPreloader(scenes, isRenderingContext)`

### 4. Package Installation
- **Added**: `@remotion/preload@4.0.325` package for audio preloading functionality

## How It Works

### Audio Detection and Filtering
1. **Extract Audio URLs**: Gets audio from `scene.voiceover?.audioUrl || scene.voiceSettings?.voiceUrl`
2. **Filter Network URLs**: Only preloads URLs that:
   - Don't start with `data:` (base64)
   - Don't start with `blob:` (already in memory)
   - Are valid non-empty strings
3. **Deduplicate**: Removes duplicate URLs to optimize preloading

### Preloading Process
1. **Buffer Control**: Uses `useBufferState().delayPlayback()` to pause the player
2. **Parallel Loading**: Uses `Promise.all()` to preload all audio simultaneously
3. **Timeout Protection**: 5-second timeout prevents infinite waiting
4. **Completion**: Calls `delayHandle.unblock()` to resume playback

### Fallback Protection
- `pauseWhenBuffering` prop on all `<Audio>` components
- Automatically pauses playback if audio is still buffering
- Resumes when audio is ready

## Testing Instructions

### Manual Testing
1. **Create scenes with Supabase audio URLs**:
   - Generate voiceover using Inngest automation (creates Supabase URLs)
   - Regenerate voiceover (creates base64 data)
   - Test both scenarios

2. **Check console logs during preview**:
   ```
   🎵 Preloading X audio files for preview...
   ✅ Audio 1/X preloaded: https://fqoolhppmbqqzxqqtjbn.supabase.co/...
   ✅ All audio files preloaded successfully
   ```

3. **Verify smooth playback**:
   - Play the preview and ensure no silent gaps at the beginning
   - Audio should start immediately without delays
   - Base64 audio should continue working perfectly

### Performance Testing
```bash
# Test the preview (should trigger audio preloading)
bun run dev

# Test video rendering (should NOT trigger audio preloading)
bun run remotion:render
```

## Performance Impact

### Positive Impacts
- **Eliminates Silent Gaps**: Smooth audio playback from the first frame
- **Better User Experience**: Professional preview quality
- **Parallel Loading**: All audio loads simultaneously, not sequentially
- **Smart Filtering**: Only preloads what needs preloading

### Minimal Overhead
- **Preview Only**: Hook only runs during preview, not video rendering
- **Timeout Protection**: 5-second maximum delay prevents hanging
- **Memory Efficient**: Uses browser's native audio preloading
- **Deduplication**: Avoids loading the same URL multiple times

## Error Handling

1. **Individual Audio Failures**: Continue playback even if some audio fails to preload
2. **Network Timeouts**: 5-second timeout prevents infinite waiting
3. **Graceful Degradation**: Failed audio shows as silent but doesn't block playback
4. **Fallback Protection**: `pauseWhenBuffering` handles any missed cases

## Monitoring

The hook provides detailed console logging:
- Audio preloading start/completion
- Individual audio preload success/failure
- Timeout warnings
- Error details for debugging

## Comparison with Image Preloader

| Feature | Image Preloader | Audio Preloader |
|---------|----------------|-----------------|
| **Context** | Rendering only | Preview only |
| **Purpose** | Prevent white frames | Prevent silent gaps |
| **Mechanism** | `delayRender/continueRender` | `useBufferState/delayPlayback` |
| **Package** | Native browser APIs | `@remotion/preload` |
| **Timeout** | 10 seconds | 5 seconds |
| **Fallback** | None needed | `pauseWhenBuffering` |

## Future Enhancements

1. **Configurable Timeout**: Make timeout duration configurable
2. **Progress Reporting**: Report preloading progress to UI
3. **Cache Management**: Implement more sophisticated caching strategies
4. **Retry Logic**: Add retry mechanism for failed audio preloads
