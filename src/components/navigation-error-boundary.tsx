/**
 * Navigation Error Boundary
 *
 * Catches errors that occur during navigation, particularly chunk loading errors.
 * Provides a user-friendly fallback UI and automatic recovery options.
 */

'use client'

import React, { Component, ErrorInfo, ReactNode } from 'react'
import { Button } from '@/components/ui/button'
import { AlertTriangle, RefreshCw } from 'lucide-react'
import { isChunkLoadError } from '@/lib/chunk-error-handler'

interface Props {
  children: ReactNode
  fallback?: ReactNode
}

interface State {
  hasError: boolean
  error?: Error
  isChunkError: boolean
}

export class NavigationErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = {
      hasError: false,
      isChunkError: false,
    }
  }

  static getDerivedStateFromError(error: Error): State {
    const isChunk = isChunkLoadError(error)
    return {
      hasError: true,
      error,
      isChunkError: isChunk,
    }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Navigation error caught by boundary:', error, errorInfo)

    // If it's a chunk loading error, handle it automatically
    if (isChunkLoadError(error)) {
      console.log('🔄 Chunk loading error detected, initiating recovery...')
      // Don't immediately reload, let the user see the error first
      // The global handler will take care of the reload
    }
  }

  handleRetry = () => {
    if (this.state.isChunkError) {
      // For chunk errors, reload the page to get fresh chunks
      window.location.reload()
    } else {
      // For other errors, just reset the error boundary
      this.setState({ hasError: false, error: undefined, isChunkError: false })
    }
  }

  handleGoHome = () => {
    window.location.href = '/'
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback
      }

      return (
        <div className='min-h-screen flex items-center justify-center p-4'>
          <div className='max-w-md w-full text-center space-y-6'>
            <div className='space-y-2'>
              <AlertTriangle className='h-12 w-12 text-yellow-500 mx-auto' />
              <h1 className='text-2xl font-bold text-foreground'>
                {this.state.isChunkError
                  ? 'Loading Error'
                  : 'Something went wrong'}
              </h1>
              <p className='text-muted-foreground'>
                {this.state.isChunkError
                  ? 'There was an issue loading the page. This usually happens after an app update.'
                  : 'An unexpected error occurred while navigating.'}
              </p>
            </div>

            <div className='space-y-3'>
              <Button onClick={this.handleRetry} className='w-full' size='lg'>
                <RefreshCw className='h-4 w-4 mr-2' />
                {this.state.isChunkError ? 'Reload Page' : 'Try Again'}
              </Button>

              <Button
                onClick={this.handleGoHome}
                variant='outline'
                className='w-full'
                size='lg'
              >
                Go to Home
              </Button>
            </div>

            {process.env.NODE_ENV === 'development' && this.state.error && (
              <details className='text-left'>
                <summary className='text-sm text-muted-foreground cursor-pointer'>
                  Error Details (Development)
                </summary>
                <pre className='mt-2 text-xs bg-muted p-3 rounded overflow-auto'>
                  {this.state.error.message}
                  {this.state.error.stack && (
                    <>
                      {'\n\n'}
                      {this.state.error.stack}
                    </>
                  )}
                </pre>
              </details>
            )}
          </div>
        </div>
      )
    }

    return this.props.children
  }
}
