/**
 * Chunk Error Handler Component
 *
 * Initializes global chunk loading error handling for the application.
 * This component should be placed in the root layout to catch chunk loading
 * errors that occur during navigation.
 */

'use client'

import { useEffect } from 'react'
import { usePathname } from 'next/navigation'
import {
  setupGlobalChunkErrorHandler,
  resetChunkErrorRetries,
} from '@/lib/chunk-error-handler'

export function ChunkErrorHandler() {
  const pathname = usePathname()

  useEffect(() => {
    // Only set up error handling on the client side
    if (typeof window !== 'undefined') {
      setupGlobalChunkErrorHandler()
    }
  }, [])

  // Reset retry count on successful navigation
  useEffect(() => {
    resetChunkErrorRetries()
  }, [pathname])

  // This component doesn't render anything
  return null
}
