/**
 * useAudioPreloader Hook
 * 
 * Preloads audio files from Supabase storage URLs in preview mode to prevent
 * silent gaps at the beginning of audio playback. This is the counterpart to
 * useImagePreloader but specifically for preview mode audio buffering.
 * 
 * Uses Remotion's useBufferState().delayPlayback() mechanism to pause the
 * player until audio assets are preloaded and ready for smooth playback.
 */

import { useEffect } from 'react'
import { useBufferState } from 'remotion'
import { preloadAudio } from '@remotion/preload'
import type { Scene } from '../types'

export const useAudioPreloader = (
  scenes: Scene[],
  isRenderingContext: boolean
) => {
  const buffer = useBufferState()

  useEffect(() => {
    // Only run in PREVIEW mode (opposite of image preloader)
    // Audio preloading is not needed during video rendering
    if (isRenderingContext) return

    // Extract unique audio URLs from scenes, excluding base64 and blob URLs
    const audioUrls = Array.from(
      new Set(
        scenes
          .map(scene => {
            // Use same priority as ScenesVideo component
            return scene.voiceover?.audioUrl || scene.voiceSettings?.voiceUrl
          })
          .filter((url): url is string => {
            // Only preload network URLs, skip base64 and blob URLs
            return (
              url &&
              typeof url === 'string' &&
              !url.startsWith('data:') &&
              !url.startsWith('blob:') &&
              url.trim().length > 0
            )
          })
      )
    )

    // If no network audio URLs to preload, return early
    if (audioUrls.length === 0) return

    console.log(`🎵 Preloading ${audioUrls.length} audio files for preview...`)

    // Use Remotion's buffer state to pause playback until audio is ready
    const delayHandle = buffer.delayPlayback()

    // Create timeout promise to prevent infinite waiting
    const timeoutPromise = new Promise<void>((resolve) => {
      setTimeout(() => {
        console.warn('⏰ Audio preloading timeout reached (5s), continuing with playback...')
        resolve()
      }, 5000) // 5 second timeout (shorter than image timeout)
    })

    // Create preload promises for all audio URLs
    const preloadPromises = audioUrls.map(
      (url, index) =>
        preloadAudio(url)
          .then(() => {
            console.log(`✅ Audio ${index + 1}/${audioUrls.length} preloaded:`, url.substring(0, 50) + '...')
            return url
          })
          .catch((error) => {
            console.warn(`⚠️ Audio ${index + 1}/${audioUrls.length} failed to preload:`, url.substring(0, 50) + '...', error)
            // Return the URL anyway to not block other audio
            return url
          })
    )

    // Wait for all audio to preload or timeout
    Promise.race([
      Promise.all(preloadPromises).then(() => {
        console.log('✅ All audio files preloaded successfully')
      }),
      timeoutPromise
    ])
      .then(() => {
        delayHandle.unblock()
      })
      .catch((error) => {
        console.error('❌ Error during audio preloading:', error)
        // Continue playback even if preloading fails
        delayHandle.unblock()
      })

    // Cleanup function to ensure handle is always unblocked
    return () => {
      try {
        delayHandle.unblock()
      } catch (error) {
        // Handle might already be resolved
        console.debug('Audio preloader cleanup:', error)
      }
    }
  }, [scenes, isRenderingContext, buffer])
}
