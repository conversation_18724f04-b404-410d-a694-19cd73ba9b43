/**
 * UnifiedComposition
 *
 * Single, centralized composition that handles both browser preview and video rendering.
 * Eliminates code duplication while maintaining all existing functionality and performance optimizations.
 *
 * Key Features:
 * - Context-aware: Detects preview vs rendering environment
 * - Flexible props: Uses explicit props or falls back to store data
 * - Unified scene filtering: Consistent logic for all workflows
 * - Performance optimized: React.memo, useMemo, conditional store access
 * - Font loading: Only applies delayRender/continueRender in rendering context
 */

import React, { useMemo } from 'react'
import { delayRender, continueRender } from 'remotion'
import type { Scene, CaptionStyle } from '../types'
import { ScenesVideo } from '../components'
import { useVideoStore } from '../../../store/video-store'
import { useImagePreloader } from '../hooks'

interface MusicTrack {
  id: string
  title: string
  genre: string
  mood: string
  artistName: string
  artistUrl?: string
  provider: string
  licenseId: string
  sourceUrl?: string
  previewUrl: string
  durationMillis: number
}

interface SpeechData {
  enabled: boolean
  src: string
  name: string
  volume: number
  transcript: {
    captions: Array<{
      start: number
      end: number
      sentence: string
      wordBoundries: Array<{ start: number; end: number; word: string }>
    }>
    status: string
  }
}

interface UnifiedCompositionProps {
  // Core props (always required)
  scenes: Scene[]
  subtitlePosition?: { x: number; y: number }
  captionStyle?: CaptionStyle

  // Optional props (fallback to store if not provided in preview mode)
  selectedMusic?: MusicTrack | null
  musicVolume?: number
  musicEnabled?: boolean
  compositionWidth?: number
  compositionHeight?: number
  speech?: SpeechData | null

  // Context control
  isRenderingContext?: boolean
}

const UnifiedCompositionComponent: React.FC<UnifiedCompositionProps> = ({
  scenes,
  subtitlePosition,
  captionStyle,
  selectedMusic,
  musicVolume,
  musicEnabled,
  compositionWidth = 960,
  compositionHeight = 540,
  speech,
  isRenderingContext = false,
}) => {
  console.log(
    `🎬 UnifiedComposition ${isRenderingContext ? 'RENDERING' : 'PREVIEW'} mode`
  )

  // Always call the hook to satisfy Rules of Hooks, but only use data in preview context
  const storeData = useVideoStore()

  // Use explicit props or fallback to store data (preview mode only)
  const effectiveMusic =
    selectedMusic ??
    (isRenderingContext ? null : storeData?.selectedMusic) ??
    null
  const effectiveMusicVolume =
    musicVolume ?? (isRenderingContext ? 50 : storeData?.musicVolume) ?? 50
  const effectiveMusicEnabled =
    musicEnabled ??
    (isRenderingContext ? true : storeData?.musicEnabled) ??
    true
  const effectiveSpeech =
    speech ?? (isRenderingContext ? null : storeData?.project?.speech) ?? null

  // Font loading only in rendering context (critical for video export)
  React.useEffect(() => {
    if (
      !isRenderingContext ||
      !captionStyle?.fontFamily ||
      captionStyle.fontFamily === 'Inter'
    ) {
      return
    }

    const fontFamily = captionStyle.fontFamily
    const formattedFontFamily = fontFamily.replace(/\s+/g, '+')
    const fontUrl = `https://fonts.googleapis.com/css2?family=${formattedFontFamily}:wght@400;700&display=swap`

    console.log('🔤 Loading font for rendering:', fontFamily)

    // Use Remotion's delayRender to block rendering until font is loaded
    const handle = delayRender(`Loading font: ${fontFamily}`)

    // Check if font is already loaded
    const existingLink = document.querySelector(`link[href="${fontUrl}"]`)
    if (existingLink) {
      console.log('🔤 Font already loaded:', fontFamily)
      continueRender(handle)
      return
    }

    // Create and inject CSS link with proper loading detection
    const link = document.createElement('link')
    link.rel = 'stylesheet'
    link.href = fontUrl

    const loadFont = () => {
      return new Promise<void>((resolve, reject) => {
        link.onload = () => {
          console.log('✅ Font loaded successfully:', fontFamily)
          resolve()
        }
        link.onerror = () => {
          console.error('❌ Failed to load font:', fontFamily)
          reject(new Error(`Failed to load font: ${fontFamily}`))
        }

        document.head.appendChild(link)

        // Set a timeout as fallback (5 seconds)
        setTimeout(() => {
          console.warn(
            '⚠️ Font loading timeout, continuing anyway:',
            fontFamily
          )
          resolve()
        }, 5000)
      })
    }

    loadFont()
      .then(() => {
        continueRender(handle)
      })
      .catch(error => {
        console.error('Font loading error:', error)
        continueRender(handle) // Continue even if font fails to load
      })
  }, [isRenderingContext, captionStyle?.fontFamily])

  // Image preloading for rendering context to prevent white frames
  useImagePreloader(scenes, isRenderingContext)

  // Unified scene filtering logic that handles all workflow types
  const validScenes = useMemo(() => {
    return scenes.filter((scene): scene is Scene => {
      if (effectiveSpeech) {
        // Speech-based workflows (podcast, audio-to-video): scenes have duration but no voiceover
        return typeof scene.duration === 'number' && scene.duration > 0
      } else {
        // Text-based workflows (text-to-video, blog-to-video): scenes have voiceover with audioDuration
        return (
          typeof scene.voiceover?.audioDuration === 'number' &&
          scene.voiceover.audioDuration > 0
        )
      }
    })
  }, [scenes, effectiveSpeech])

  // Memoize music settings to prevent unnecessary re-renders
  const musicSettings = useMemo(
    () => ({
      selectedMusic: effectiveMusic,
      musicVolume: effectiveMusicVolume,
      musicEnabled: effectiveMusicEnabled,
    }),
    [effectiveMusic, effectiveMusicVolume, effectiveMusicEnabled]
  )

  return (
    <ScenesVideo
      scenes={validScenes}
      subtitlePosition={subtitlePosition}
      selectedMusic={musicSettings.selectedMusic}
      musicVolume={musicSettings.musicVolume}
      musicEnabled={musicSettings.musicEnabled}
      compositionWidth={compositionWidth}
      compositionHeight={compositionHeight}
      speech={effectiveSpeech}
      captionStyle={captionStyle}
    />
  )
}

UnifiedCompositionComponent.displayName = 'UnifiedComposition'

export const UnifiedComposition = React.memo(UnifiedCompositionComponent)
