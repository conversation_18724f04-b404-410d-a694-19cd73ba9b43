'use client'

import { useState, useEffect } from 'react'

export function useInngestRunStatus(eventId: string | null) {
  const [status, setStatus] = useState<string | null>(null)
  const [output, setOutput] = useState<unknown>(null)
  const [error, setError] = useState<string | null>(null)
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (!eventId) return
    let cancelled = false
    setLoading(true)
    setStatus(null)
    setOutput(null)
    setError(null)

    async function poll() {
      try {
        const res = await fetch(`/api/video-data-status?eventId=${eventId}`)
        if (!res.ok) throw new Error('Failed to fetch run status')
        const runs = await res.json()
        if (!Array.isArray(runs) || runs.length === 0)
          throw new Error('No runs found')
        const run = runs[0]
        if (cancelled) return
        setStatus(run.status)
        if (run.status === 'Completed') {
          setOutput(run.output)
          // // save eventId, runId in project db
          // const [inserted] = await db.insert(projects).values(insertData)

          setLoading(false)
        } else if (run.status === 'Failed' || run.status === 'Cancelled') {
          setError(`Run ${run.status}`)
          setLoading(false)
        } else {
          setTimeout(poll, 1500)
        }
      } catch (err: unknown) {
        if (cancelled) return
        setError(err instanceof Error ? err.message : 'Unknown error')
        setLoading(false)
      }
    }
    poll()
    return () => {
      cancelled = true
    }
  }, [eventId])

  return { status, output, error, loading }
}
