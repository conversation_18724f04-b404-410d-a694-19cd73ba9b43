'use client'

import { useState, useEffect } from 'react'
import { useSearchParams } from 'next/navigation'
import {
  VideoPreviewLayout,
  VideoPlayerContainer,
  VideoEditorHeader,
  ScenesSidebar,
  SceneEditorSkeleton,
} from '../_components'
import { Modal } from '@/components/ui/modal'
import { VideoCard } from '@/app/(dashboard)/my-videos/_components/VideoCard'

import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { useVideoStore } from '@/store/video-store'
import { useProjectData } from '@/hooks/useProjectData'
import { useProjectSync } from '@/hooks/useProjectSync'
import { useNavigationGuard } from '@/hooks/useNavigationGuard'
import { useCaptionStylesStore } from '@/store'
import { toast } from '@/lib/toast'
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from '@/components/ui/select'
import { useRenderJobs } from '@/hooks/useRenderJobs'
import { Loader2, RefreshCw } from 'lucide-react'
import { Scene } from '@/types/video'

export default function SceneEditorPage() {
  // Mobile toggle state: 'editor' | 'preview'
  const [mobileTab, setMobileTab] = useState<'editor' | 'preview'>('editor')
  const [showRecents, setShowRecents] = useState(false)
  const [showExport, setShowExport] = useState(false)
  const [exportName, setExportName] = useState('My Exported Video')
  const [exportResolution, setExportResolution] = useState('1080p')
  const [isExporting, setIsExporting] = useState(false)

  // Get projectId from URL params
  const searchParams = useSearchParams()
  const projectId = searchParams.get('projectId') ?? null

  // Load project data
  const { isLoading, error, projectLoaded } = useProjectData(projectId)
  const {
    scenes,
    project,
    subtitlePosition,
    orientation,
    selectedMusic,
    musicVolume,
    musicEnabled,
  } = useVideoStore()
  const { getEffectiveStyle } = useCaptionStylesStore()

  // Project synchronization
  const { isSaving, lastSaved, hasUnsavedChanges, forceSave } = useProjectSync({
    projectId,
    enabled: !!projectId && projectLoaded,
  })

  // Navigation guard to prevent leaving with unsaved changes
  useNavigationGuard({
    hasUnsavedChanges,
    isSaving,
    message:
      'You have unsaved changes in your video project. Are you sure you want to leave without saving?',
  })

  // Update export name when project loads
  useEffect(() => {
    if (project?.projectName) {
      setExportName(project.projectName)
    }
  }, [project?.projectName])

  // Warn about unsaved changes on page unload
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (hasUnsavedChanges) {
        e.preventDefault()
        e.returnValue =
          'You have unsaved changes. Are you sure you want to leave?'
        return e.returnValue
      }
    }

    window.addEventListener('beforeunload', handleBeforeUnload)
    return () => window.removeEventListener('beforeunload', handleBeforeUnload)
  }, [hasUnsavedChanges])

  const {
    renderJobs,
    isLoading: isLoadingJobs,
    refreshJobs,
    connectionStatus,
    updateCounter,
  } = useRenderJobs(showRecents ? (projectId ?? undefined) : undefined)

  // Optimistic local state for Recent Exports
  const [localRenderJobs, setLocalRenderJobs] = useState(renderJobs)
  useEffect(() => {
    setLocalRenderJobs(renderJobs)
  }, [renderJobs])

  // Debug logging for render jobs
  console.log('🎬 Scene Editor render jobs:', {
    count: renderJobs.length,
    jobs: renderJobs.map(job => ({
      id: job.id,
      status: job.status,
      progress: job.progress,
    })),
    connectionStatus,
    updateCounter,
    showRecents,
    projectId,
  })

  const totalDuration = scenes.reduce(
    (acc, scene) => acc + (scene.duration || 0),
    0
  )
  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  // Handle video export
  const handleExport = async () => {
    // Filter valid scenes for export
    const validScenes = scenes.filter(
      scene => typeof scene.duration === 'number' && scene.duration > 0
    )

    if (validScenes.length === 0) {
      toast.warning('Please add scenes with voiceover before exporting')
      return
    }

    setIsExporting(true)
    const renderToastId = toast.renderProgress('Preparing video...')

    try {
      // Save project before exporting
      await forceSave()

      // Get the most current subtitle position directly from localStorage
      let currentPosition = { ...subtitlePosition }
      try {
        const savedPosition = localStorage.getItem('subtitlePosition')
        if (savedPosition) {
          currentPosition = JSON.parse(savedPosition)
          console.log(
            '🎯 Using most current position for rendering:',
            currentPosition
          )
        }
      } catch (e) {
        console.error('Error getting current subtitle position', e)
      }

      // Calculate composition dimensions based on export resolution and current orientation
      const standardDimensions = {
        landscape: {
          '1080p': { width: 1920, height: 1080 },
          '720p': { width: 1280, height: 720 },
          '480p': { width: 854, height: 480 },
        },
        portrait: {
          '1080p': { width: 1080, height: 1920 },
          '720p': { width: 720, height: 1280 },
          '480p': { width: 480, height: 854 },
        },
        square: {
          '1080p': { width: 1920, height: 1920 },
          '720p': { width: 1280, height: 1280 },
          '480p': { width: 854, height: 854 },
        },
      } as const

      const orientationDimensions =
        standardDimensions[orientation as keyof typeof standardDimensions] ||
        standardDimensions.landscape
      const dimensions =
        orientationDimensions[
          exportResolution as keyof typeof orientationDimensions
        ] || standardDimensions.landscape['1080p']
      const compositionWidth = dimensions.width
      const compositionHeight = dimensions.height

      toast.renderProgress('Submitting render job...', renderToastId)
      // await innjestRender(
      //   validScenes,
      //   compositionHeight,
      //   compositionWidth,
      //   currentPosition
      // )

      await localRender(
        validScenes,
        compositionHeight,
        compositionWidth,
        currentPosition,
        renderToastId
      )
    } catch (error) {
      console.error('Export error:', error)
      toast.error(
        error instanceof Error
          ? `Export failed: ${error.message}`
          : 'Failed to export video'
      )
    } finally {
      setIsExporting(false)
      toast.dismiss(renderToastId)
    }
  }

  const localRender = async (
    validScenes: Scene[],
    compositionHeight: 1920 | 1080 | 1280 | 720 | 854 | 480,
    compositionWidth: 1920 | 1080 | 1280 | 720 | 854 | 480,
    currentPosition: { x: number; y: number },
    renderToastId: string | number
  ) => {
    console.log(
      'Rendering with orientation:',
      orientation,
      'resolution:',
      exportResolution,
      'dimensions:',
      compositionWidth,
      'x',
      compositionHeight
    )
    // Create the payload that will be sent to remotion-render
    const remotionPayload = {
      scenes: validScenes,
      subtitlePosition: currentPosition,
      orientation: orientation,
      compositionWidth: compositionWidth,
      compositionHeight: compositionHeight,
      selectedMusic: selectedMusic,
      musicVolume: musicVolume,
      musicEnabled: musicEnabled,
      captionStyle: getEffectiveStyle(), // Use current caption style from store
      speech: project?.speech, // Include speech object for audio/podcast workflows
    }

    // Log the complete payload being sent to remotion-render
    console.log('🎬 Full payload sent to /api/remotion-render:')
    console.log(JSON.stringify(remotionPayload, null, 2))

    // Also log individual parts for easier inspection
    console.log('📋 Scenes data:', validScenes)
    console.log('📍 Subtitle position:', currentPosition)
    console.log('🎵 Music data:', selectedMusic)
    console.log('🎨 Caption style:', getEffectiveStyle())
    console.log('⚙️ Export settings:', {
      orientation,
      compositionWidth,
      compositionHeight,
      musicVolume,
      musicEnabled,
      exportResolution,
    })

    const res = await fetch('/api/remotion-render', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(remotionPayload),
    })

    if (!res.ok) {
      const errorText = await res.text()
      throw new Error(`Failed to render video: ${errorText}`)
    }

    toast.renderProgress('Download starting...', renderToastId)

    // Create download blob and trigger download
    const blob = await res.blob()
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.style.display = 'none'
    a.href = url
    a.download = `${exportName || 'adori-video'}-${exportResolution}.mp4`
    document.body.appendChild(a)
    a.click()
    window.URL.revokeObjectURL(url)
    document.body.removeChild(a)

    toast.success('Video exported successfully!')
    setShowExport(false)
  }

  const innjestRender = async (
    validScenes: Scene[],
    compositionHeight: 1920 | 1080 | 1280 | 720 | 854 | 480,
    compositionWidth: 1920 | 1080 | 1280 | 720 | 854 | 480,
    currentPosition: { x: number; y: number }
  ) => {
    // Prepare payload for Inngest render function
    const inngestPayload = {
      inputProps: {
        scenes: validScenes,
        subtitlePosition: currentPosition,
        orientation: orientation,
        compositionWidth: compositionWidth,
        compositionHeight: compositionHeight,
        selectedMusic: selectedMusic,
        musicVolume: musicVolume,
        musicEnabled: musicEnabled,
        captionStyle: getEffectiveStyle(),
        exportName,
        exportResolution,
        durationInFrames: Math.round(totalDuration * 30),
        speech: project?.speech,
      },
      projectId,
      userId: project?.userId,
      exportName,
      exportResolution,
      duration: totalDuration,
    }

    // Call API route to trigger Inngest render function
    const res = await fetch('/api/render-video', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(inngestPayload),
    })

    if (!res.ok) {
      const errorText = await res.text()
      throw new Error(`Failed to start render job: ${errorText}`)
    }

    toast.success(
      'Render job started! You can track progress in Recent Exports.'
    )
    setShowExport(false)
    setShowRecents(true)
  }

  // Show loading state while fetching project data
  if (projectId && isLoading) {
    return <SceneEditorSkeleton />
  }

  // Show error state if project failed to load
  if (projectId && error) {
    return (
      <div className='flex items-center justify-center min-h-screen'>
        <div className='flex flex-col items-center gap-4 text-center'>
          <p className='text-red-500'>Failed to load project</p>
          <p className='text-muted-foreground text-sm'>{error}</p>
          <Button onClick={() => window.location.reload()}>Try Again</Button>
        </div>
      </div>
    )
  }

  // Show skeleton if projectId exists but project not loaded yet (during initial data fetch)
  if (projectId && !projectLoaded && !isLoading) {
    return <SceneEditorSkeleton />
  }

  return (
    <>
      <VideoPreviewLayout
        headerContent={
          <VideoEditorHeader
            onShowRecents={() => setShowRecents(true)}
            onShowExport={() => setShowExport(true)}
            syncStatus={{
              isSaving,
              lastSaved,
              hasUnsavedChanges,
              forceSave,
            }}
          />
        }
        mobileTab={mobileTab}
        setMobileTab={setMobileTab}
      >
        {/* Left panel: only scenes list/cards, settings are inside each card */}
        <ScenesSidebar />
        {/* Right panel: scene preview, needs fullscreen props */}
        {({ isFullscreen, handleFullscreenToggle }) => (
          <VideoPlayerContainer
            isFullscreen={isFullscreen}
            onFullscreenToggle={handleFullscreenToggle}
          />
        )}
      </VideoPreviewLayout>
      <Modal
        title='Recent Exports'
        isOpen={showRecents}
        onClose={() => setShowRecents(false)}
        size='3xl'
      >
        <div className='p-4'>
          <div className='flex items-center justify-between mb-4'>
            <h3 className='text-lg font-semibold'>Recent Exports</h3>
            <button
              type='button'
              className='p-2 rounded hover:bg-muted transition'
              onClick={refreshJobs}
              aria-label='Refresh exports'
              disabled={isLoadingJobs}
            >
              {isLoadingJobs ? (
                <Loader2 className='w-4 h-4 animate-spin' />
              ) : (
                <RefreshCw className='w-4 h-4' />
              )}
            </button>
          </div>
          <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6'>
            {isLoadingJobs ? (
              <div>Loading...</div>
            ) : localRenderJobs.length === 0 ? (
              <div className='text-muted-foreground text-center col-span-full'>
                No exports yet.
              </div>
            ) : (
              localRenderJobs.map(job => (
                <VideoCard
                  key={`${job.id}-${job.status}-${job.progress}`}
                  thumbnail={job.thumbnailUrl || null}
                  created_at={job.createdAt}
                  url={job.publicUrl || null}
                  youtubeId={job.youtubeId || null}
                  onPublish={() => {}}
                  status={job.status}
                  progress={job.progress}
                  errorMessage={job.errorMessage || undefined}
                  exportName={job.exportName || undefined}
                  exportResolution={job.exportResolution || undefined}
                  renderJobId={job.id}
                  onDeleted={() =>
                    setLocalRenderJobs(jobs =>
                      jobs.filter(j => j.id !== job.id)
                    )
                  }
                />
              ))
            )}
          </div>
        </div>
      </Modal>
      <Modal
        title='Export Video'
        isOpen={showExport}
        onClose={() => setShowExport(false)}
        size='sm'
      >
        <form className='space-y-6 p-2'>
          <div>
            <Label className='mb-1 flex'>
              Duration:{'  '}
              <div className='font-semibold'>
                {formatDuration(totalDuration)}
              </div>
            </Label>
          </div>

          <div>
            <Label htmlFor='export-resolution' className='mb-1 block'>
              Resolution
            </Label>
            <Select
              value={exportResolution}
              onValueChange={setExportResolution}
            >
              <SelectTrigger className='w-full'>
                <SelectValue placeholder='Select resolution' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='1080p'>1080p</SelectItem>
                <SelectItem value='720p'>720p</SelectItem>
                <SelectItem value='480p'>480p</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className='pt-2 flex justify-between'>
            {isSaving ? 'Saving...' : ''}
            <Button
              type='submit'
              onClick={async e => {
                e.preventDefault()
                await handleExport()
              }}
              disabled={isExporting}
            >
              {isExporting ? 'Exporting...' : 'Export'}
            </Button>
          </div>
        </form>
      </Modal>
    </>
  )
}
