import { NextRequest, NextResponse } from 'next/server'

export async function GET(req: NextRequest) {
  const eventId = req.nextUrl.searchParams.get('eventId')
  if (!eventId) {
    return NextResponse.json(
      { error: 'Missing or invalid eventId' },
      { status: 400 }
    )
  }
  try {
    const baseUrl =
      process.env.INNGEST_API_BASE_URL || 'http://localhost:8288/v1'
    const inngestRes = await fetch(`${baseUrl}/events/${eventId}/runs`, {
      headers: {
        Authorization: `Bearer ${process.env.INNGEST_SIGNING_KEY}`,
      },
    })
    if (!inngestRes.ok) {
      return NextResponse.json(
        { error: 'Failed to fetch from Inngest' },
        { status: inngestRes.status }
      )
    }
    const json = await inngestRes.json()
    console.log(json.data[0].run_id)
    return NextResponse.json(json.data)
  } catch {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
