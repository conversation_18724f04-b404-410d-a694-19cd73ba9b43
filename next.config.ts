import type { NextConfig } from 'next'

const nextConfig: NextConfig = {
  // Performance optimizations for Next.js 15
  experimental: {
    // Configure client-side router cache stale times for better performance
    staleTimes: {
      dynamic: 30, // 30 seconds for dynamic routes
      static: 180, // 3 minutes for static routes
    },
    // Optimize package imports to reduce bundle size
    optimizePackageImports: [
      'lucide-react',
      '@radix-ui/react-icons',
      '@clerk/nextjs',
      '@clerk/themes',
      'framer-motion',
      'date-fns',
    ],
  },

  // Image optimization
  images: {
    // Use remotePatterns for better security and flexibility
    remotePatterns: [
      // Allow all HTTPS images (most secure approach for external images)
      {
        protocol: 'https',
        hostname: '**',
      },
      // Allow HTTP for localhost development
      {
        protocol: 'http',
        hostname: 'localhost',
      },
      {
        protocol: 'http',
        hostname: '127.0.0.1',
      },
    ],
    // Enable modern image formats for better compression
    formats: ['image/webp', 'image/avif'],
    // Optimize image sizes for different devices
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    // Cache optimized images longer
    minimumCacheTTL: 86400, // 24 hours
    // Allow data URLs for base64 images
    dangerouslyAllowSVG: true,
    contentDispositionType: 'attachment',
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
    // Loader configuration for better performance
    loader: 'default',
    // Disable static imports for better tree shaking
    unoptimized: false,
  },

  // Compiler optimizations
  compiler: {
    // Note: removeConsole affects ALL JavaScript including API routes
    // Using webpack solution instead for client-only console removal
  },

  // Performance and caching
  poweredByHeader: false,
  compress: true,

  // PostHog rewrites
  async rewrites() {
    return [
      {
        source: '/ingest/static/:path*',
        destination: 'https://us-assets.i.posthog.com/static/:path*',
      },
      {
        source: '/ingest/:path*',
        destination: 'https://us.i.posthog.com/:path*',
      },
      {
        source: '/ingest/decide',
        destination: 'https://us.i.posthog.com/decide',
      },
    ]
  },

  // This is required to support PostHog trailing slash API requests
  skipTrailingSlashRedirect: true,

  // Fix: Ignore .md files (e.g. @esbuild/darwin-arm64/README.md) to prevent build errors with Remotion/Next.js
  webpack(config, { dev, isServer }) {
    config.module.rules.push(
      {
        test: /\.md$/,
        use: 'ignore-loader',
      },
      {
        test: /\.d\.ts$/,
        use: 'ignore-loader',
      }
    )

    // Remove console.log statements from client-side bundles ONLY in production
    // This preserves ALL server-side logging (API routes, middleware, server components)
    if (!dev && !isServer) {
      // Use Terser plugin to remove console statements from client bundles only

      const TerserPlugin = require('terser-webpack-plugin')

      // Ensure optimization object exists
      config.optimization = config.optimization || {}
      config.optimization.minimizer = config.optimization.minimizer || []

      // Find existing TerserPlugin and modify it
      const existingTerserIndex = config.optimization.minimizer.findIndex(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (plugin: any) => plugin.constructor.name === 'TerserPlugin'
      )

      const terserOptions = {
        terserOptions: {
          compress: {
            // Remove console.* statements except error and warn
            drop_console: false, // Don't drop all console
            pure_funcs: [
              'console.log',
              'console.info',
              'console.debug',
              'console.trace',
              'console.table',
              'console.time',
              'console.timeEnd',
              'console.group',
              'console.groupEnd',
              'console.groupCollapsed',
            ], // Only remove these specific console methods
          },
        },
        extractComments: false,
      }

      if (existingTerserIndex >= 0) {
        // Modify existing TerserPlugin
        const existingTerser =
          config.optimization.minimizer[existingTerserIndex]
        config.optimization.minimizer[existingTerserIndex] = new TerserPlugin({
          ...existingTerser.options,
          ...terserOptions,
        })
      } else {
        // Add new TerserPlugin
        config.optimization.minimizer.push(new TerserPlugin(terserOptions))
      }
    }

    // Optimize bundle splitting in production
    if (!dev && !isServer) {
      config.optimization.splitChunks = {
        ...config.optimization.splitChunks,
        cacheGroups: {
          ...config.optimization.splitChunks?.cacheGroups,
          // Separate vendor chunks for better caching
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all',
            priority: 10,
          },
          // Separate UI components chunk
          ui: {
            test: /[\\/]src[\\/]components[\\/]ui[\\/]/,
            name: 'ui',
            chunks: 'all',
            priority: 20,
          },
        },
      }
    }

    return config
  },
}

export default nextConfig
